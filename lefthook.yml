pre-commit:
  commands:
    format:
      glob: "*.{js,ts,cjs,mjs,d.cts,d.mts,jsx,tsx,json,jsonc}"
      run: bunx biome format --write --no-errors-on-unmatched --files-ignore-unknown=true {staged_files}
      stage_fixed: true
    lint:
      glob: "*.{js,ts,cjs,mjs,d.cts,d.mts,jsx,tsx,json,jsonc}"
      run: bunx biome lint --apply --no-errors-on-unmatched --files-ignore-unknown=true {staged_files}
      stage_fixed: true
    check:
      glob: "*.{js,ts,cjs,mjs,d.cts,d.mts,jsx,tsx,json,jsonc}"
      run: bunx biome check --no-errors-on-unmatched --files-ignore-unknown=true {staged_files}
    spellcheck:
      glob: "**/*.ts"
      run: bunx cspell {staged_files} || echo "Spell check completed with warnings"
